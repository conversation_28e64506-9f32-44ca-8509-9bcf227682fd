import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/context/AuthProvider";
import { ReactNode } from "react";
import { Toaster } from "sonner";
import { CartProvider } from "@/context/CartProvider";
import { CurrencyProvider } from "@/context/CurrencyProvider";
import { Providers } from "@/context/StripeProvider";
import { SocketProvider } from "@/context/SocketContext";
import NotificationStoreInitializer from "@/components/NotificationStoreInitializer";
import ErrorBoundary from "@/components/ErrorBoundary";
const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "FamFoody",
  description: "FamFoody is a food service",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body>
        <Toaster position="bottom-left" richColors duration={2} />
        <AuthProvider>
          <SocketProvider>
            <NotificationStoreInitializer />
            <CartProvider>
              <Providers>
                <CurrencyProvider>{children}</CurrencyProvider>
              </Providers>
            </CartProvider>
          </SocketProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
