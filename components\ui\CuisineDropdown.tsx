"use client";

import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";

interface CuisineOption {
  label: string;
  value: string;
}

interface CuisineDropdownProps {
  cuisines: CuisineOption[];
  selectedCuisine: string;
  onCuisineSelect: (cuisine: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

const CuisineDropdown: React.FC<CuisineDropdownProps> = ({
  cuisines,
  selectedCuisine,
  onCuisineSelect,
  disabled = false,
  placeholder = "Cuisine",
}) => {
  // Find the selected cuisine label
  const selectedCuisineLabel = cuisines.find(
    (cuisine) => cuisine.value === selectedCuisine
  )?.label || placeholder;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 px-3 text-sm border-none focus-visible:ring-0 shadow-none justify-between min-w-[100px] max-w-[150px]"
          disabled={disabled}
        >
          <span className="truncate">
            {selectedCuisineLabel}
          </span>
          <ChevronDown className="h-3 w-3 ml-1 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56 max-h-64 overflow-y-auto">
        {cuisines.map((cuisine) => (
          <DropdownMenuItem
            key={cuisine.value}
            onClick={() => onCuisineSelect(cuisine.value)}
            className={`cursor-pointer ${
              selectedCuisine === cuisine.value
                ? "bg-primary/10 text-primary font-medium"
                : ""
            }`}
          >
            {cuisine.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CuisineDropdown;
