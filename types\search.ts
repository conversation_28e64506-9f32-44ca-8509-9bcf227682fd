/**
 * Enhanced Search Interface - TypeScript Type Definitions
 * Comprehensive interfaces for the 5-step progressive search system
 */

// ===== Core Search State =====

export interface SearchStepsState {
  activeStep: number;
  completedSteps: Set<number>;
  stepData: SearchStepData;
}

export interface SearchStepData {
  places: PlaceData;
  dishName: string;
  dates: DateData;
  offering: string;
  cuisines: string[];
}

// ===== Individual Step Data Types =====

export interface PlaceData {
  address: string;
  lat?: number;
  lng?: number;
  placeId?: string;
  isCustom: boolean;
  type: "nearby" | "popular" | "custom";
}

export interface DateData {
  mode: "single" | "range" | "flexible";
  startDate?: Date;
  endDate?: Date;
  flexibleDays?: number; // ±1-3 days for flexible mode
}

export interface DishData {
  name: string;
  isPopular: boolean;
  category?: string;
}

export interface OfferingData {
  type: "dine-in" | "delivery" | "pickup" | "";
  label: string;
}

export interface CuisineData {
  id: string;
  name: string;
  isSelected: boolean;
}

// ===== Search API Response Types =====

export interface SearchApiResponse {
  dishes: DishResult[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface DishResult {
  id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  cuisine: string[];
  hostId: string;
  hostName: string;
  location: {
    address: string;
    lat: number;
    lng: number;
  };
  images: string[];
  rating?: number;
  reviewCount?: number;
  availability: {
    offering: string[];
    dates: string[];
  };
}

export interface CuisineApiResponse {
  cuisines: CuisineOption[];
  total: number;
}

export interface CuisineOption {
  id: string;
  name: string;
  count: number;
  isPopular: boolean;
}

export interface PopularDishesApiResponse {
  dishes: PopularDish[];
  lastUpdated: string;
}

export interface PopularDish {
  name: string;
  searchCount: number;
  category: string;
  trending: boolean;
}

// ===== Component Prop Interfaces =====

export interface SearchStepContainerProps {
  stepNumber: number;
  title: string;
  isActive: boolean;
  isCompleted: boolean;
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export interface PlacesStepProps {
  value: PlaceData;
  onChange: (data: PlaceData) => void;
  onNext?: () => void;
  popularCities?: string[];
  disabled?: boolean;
}

export interface DishNameStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext?: () => void;
  popularDishes?: PopularDish[];
  disabled?: boolean;
}

export interface DateSelectionStepProps {
  value: DateData;
  onChange: (data: DateData) => void;
  onNext?: () => void;
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
}

export interface OfferingTypeStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext?: () => void;
  options?: OfferingOption[];
  disabled?: boolean;
}

export interface CuisineFiltersStepProps {
  value: string[];
  onChange: (values: string[]) => void;
  onNext?: () => void;
  cuisines?: CuisineOption[];
  loading?: boolean;
  disabled?: boolean;
}

export interface EnhancedSearchInterfaceProps {
  onSearch: (params: SearchParams) => void;
  onClearFilters: () => void;
  initialValues?: Partial<SearchStepData>;
  loading?: boolean;
  className?: string;
}

// ===== Search Parameters =====

export interface SearchParams {
  address?: string;
  lat?: number;
  lng?: number;
  dishName?: string;
  title?: string; // Date field mapping
  offering?: string;
  cuisinequery?: string; // Comma-separated cuisine IDs
  startDate?: string;
  endDate?: string;
  flexibleDays?: number;
}

// ===== Hook Interfaces =====

export interface UseSearchStepsHook {
  state: SearchStepsState;
  actions: SearchStepsActions;
  validateCurrentStep: () => {
    isValid: boolean;
    errors: string[];
  };
  getStepSummary: (stepNumber: SearchStepNumber) => string;
  stepHasData: (stepNumber: SearchStepNumber) => boolean;
}

export interface SearchStepsActions {
  setActiveStep: (step: number) => void;
  completeStep: (step: number) => void;
  updateStepData: <K extends keyof SearchStepData>(
    step: K,
    data: SearchStepData[K]
  ) => void;
  resetAllSteps: () => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  canProceedToNext: () => boolean;
}

// ===== Configuration Types =====

export interface OfferingOption {
  value: string;
  label: string;
  icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  description?: string;
}

export interface PopularCity {
  name: string;
  value: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface SearchStepConfig {
  id: number;
  title: string;
  required: boolean;
  component: React.ComponentType<any>;
  validation?: (value: any) => boolean | string;
}

// ===== Animation and UI Types =====

export interface StepTransition {
  duration: number;
  easing: string;
  property: string;
}

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
}

export interface SearchInterfaceTheme {
  colors: {
    primary: string;
    secondary: string;
    border: string;
    background: string;
    active: string;
    completed: string;
  };
  spacing: {
    step: string;
    container: string;
    content: string;
  };
  borderRadius: {
    container: string;
    step: string;
  };
  shadows: {
    container: string;
    step: string;
    active: string;
  };
}

// ===== Error and Loading States =====

export interface SearchError {
  code: string;
  message: string;
  step?: number;
  field?: string;
}

export interface LoadingState {
  step?: number;
  action: "fetching" | "searching" | "validating";
  message?: string;
}

// ===== Analytics and Tracking =====

export interface SearchAnalytics {
  stepStartTime: Record<number, number>;
  stepCompletionTime: Record<number, number>;
  abandonmentStep?: number;
  searchQuery: SearchParams;
  resultCount?: number;
}

export interface StepAnalyticsEvent {
  stepNumber: number;
  action: "enter" | "complete" | "abandon" | "edit";
  timestamp: number;
  data?: any;
}

// ===== Utility Types =====

export type SearchStepNumber = 1 | 2 | 3 | 4 | 5;

export type StepStatus = "inactive" | "active" | "completed";

export type DateMode = "single" | "range" | "flexible";

export type OfferingType = "dine-in" | "delivery" | "pickup";

export type PlaceType = "nearby" | "popular" | "custom";

// ===== Form Validation Types =====

export interface StepValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface SearchFormValidation {
  [key: number]: StepValidationResult;
}

// ===== Progressive Disclosure Types =====

export interface StepVisibility {
  isExpanded: boolean;
  showContent: boolean;
  showSummary: boolean;
  animationState: "idle" | "expanding" | "collapsing";
}

export interface SearchInterfaceState {
  steps: Record<number, StepVisibility>;
  isSearching: boolean;
  hasResults: boolean;
  error?: SearchError;
}
