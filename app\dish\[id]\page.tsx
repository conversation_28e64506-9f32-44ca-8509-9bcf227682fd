"use client";

import React, { useEffect, useState } from "react";
import { HeaderBase } from "@/components/layouts/core/header-base";
import Image from "next/image";
import { notFound, useRouter } from "next/navigation";
import {
  Heart,
  MapPin,
  Star,
  ChevronLeft,
  ChevronRight,
  Users,
  Clock,
  DollarSign,
  Maximize2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { getDishDetails } from "@/api/host";
import { useBoolean } from "@/hooks/use-boolean";
import { DishBookingCard } from "@/components/cards/DishBookingCard";
import { useCartContext } from "@/context/hooks/use-cart-hook";
import { ImageGalleryDrawer } from "@/components/ui/image-gallery-drawer";
import { Spinner } from "@/components/ui/spinner";
import { Skeleton } from "@/components/ui/skeleton";

export default function DishPage({ params }: { params: { id: string } }) {
  const hamburgerMenu = useBoolean(false);
  const [dish, setDish] = useState<any | null>(null);
  const [host, setHost] = useState<any | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentLocationImageIndex, setCurrentLocationImageIndex] = useState(0);
  const { cartItems, createCartItem, getCart } = useCartContext();
  const [dishInCart, setDishInCart] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Gallery drawer state
  const [galleryOpen, setGalleryOpen] = useState(false);
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [galleryInitialIndex, setGalleryInitialIndex] = useState(0);
  const [galleryTitle, setGalleryTitle] = useState("");

  useEffect(() => {
    const fetchDish = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const dishData = await getDishDetails(params.id);
        console.log("🚀 ~ dishData", dishData);
        if (dishData) {
          setDish(dishData.data);
          setHost(dishData.host);
        } else {
          notFound();
        }
      } catch (error) {
        console.error("Failed to fetch dish:", error);
        setError("Failed to load dish details. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDish();
    console.log("🚀 ~ params.id", params.id);
    console.log("Cart Items", cartItems);
  }, [params.id]);

  useEffect(() => {
    if (dish && host && cartItems) {
      const hostIndex = cartItems.findIndex(
        (item) => item.hostId._id === host._id
      );
      console.log("Host Index", hostIndex);
      if (hostIndex === -1) return;
      const dishInCart = cartItems[hostIndex].items.some(
        (item) => item.dishId === dish._id
      );
      console.log("Dish in Cart", dishInCart);
      setDishInCart(dishInCart);
    }
  }, [cartItems, host, dish]);

  // Loading state
  if (isLoading) {
    return (
      <React.Fragment>
        <HeaderBase hamburgerMenu={hamburgerMenu} />
        <div
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
          style={{ marginTop: "4rem" }}
        >
          {/* Loading skeleton for dish details */}
          <div className="mb-8">
            <Skeleton className="h-10 w-3/4 mb-2" />
            <div className="flex items-center space-x-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>

          {/* Loading skeleton for image gallery */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="relative">
              <Skeleton className="aspect-square w-full rounded-lg" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="aspect-square w-full rounded-lg" />
              <Skeleton className="aspect-square w-full rounded-lg" />
              <Skeleton className="aspect-square w-full rounded-lg" />
              <Skeleton className="aspect-square w-full rounded-lg" />
            </div>
          </div>

          {/* Loading skeleton for content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2 space-y-6">
              <div className="flex items-center justify-between">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-10 w-10 rounded-md" />
              </div>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-6 w-32" />
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-20 rounded-full" />
                  <Skeleton className="h-6 w-18 rounded-full" />
                </div>
              </div>
            </div>
            <div>
              <Skeleton className="h-96 w-full rounded-lg" />
            </div>
          </div>

          {/* Loading indicator with spinner */}
          <div className="flex justify-center items-center mt-8">
            <Spinner size="md" className="text-primary" />
            <span className="ml-2 text-gray-600">Loading dish details...</span>
          </div>
        </div>
      </React.Fragment>
    );
  }

  // Error state
  if (error) {
    return (
      <React.Fragment>
        <HeaderBase hamburgerMenu={hamburgerMenu} />
        <div
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
          style={{ marginTop: "4rem" }}
        >
          <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
            <div className="text-red-500 mb-4">
              <svg
                className="w-16 h-16 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Unable to Load Dish
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </React.Fragment>
    );
  }

  // Return null if no dish data (shouldn't happen with proper loading/error states)
  if (!dish) {
    return null;
  }

  const discountedPrice = dish.discount
    ? dish.price - (dish.price * dish.discount) / 100
    : dish.price;

  const nextLocationImage = () => {
    setCurrentLocationImageIndex(
      (prevIndex) => (prevIndex + 1) % dish.diningLocation.images.length
    );
  };

  const prevLocationImage = () => {
    setCurrentLocationImageIndex(
      (prevIndex) =>
        (prevIndex - 1 + dish.diningLocation.images.length) %
        dish.diningLocation.images.length
    );
  };

  const addToCart = async (
    selectedDate: Date | null,
    quantity: number,
    orderType: "Delivery" | "Take Away" | "Dine In"
  ) => {
    console.log("Adding to cart", selectedDate, quantity, dish);
    try {
      await createCartItem(
        dish._id,
        dish.name,
        dish.price,
        quantity,
        dish.photos[0],
        orderType,
        host._id,
        orderType === "Dine In" ? selectedDate : null,
        orderType === "Take Away" ? selectedDate : null
      );
      router.push("/cart");
    } catch (error) {
      console.log("Error:", error);
    }
  };

  const openDishGallery = (index = 0) => {
    setGalleryImages(dish.photos);
    setGalleryInitialIndex(index);
    setGalleryTitle(dish.name);
    setGalleryOpen(true);
  };

  const openLocationGallery = (index = 0) => {
    setGalleryImages(dish.diningLocation.images);
    setGalleryInitialIndex(index);
    setGalleryTitle(dish.diningLocation.locationName);
    setGalleryOpen(true);
  };

  return (
    <React.Fragment>
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
        style={{ marginTop: "4rem" }}
      >
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-2">{dish.name}</h1>
          <div className="flex items-center text-sm">
            <Star className="w-5 h-5 text-yellow-400 mr-1" />
            <span className="font-semibold mr-2">
              {dish.averageRating.toFixed(1)}
            </span>
            <span className="text-gray-500 mr-2">(No reviews yet)</span>
            <span className="text-gray-500">
              {dish.diningLocation.locationName}, {host.address.city},{" "}
              {host.address.state}
            </span>
          </div>
        </div>

        <div className="relative mb-8">
          <div className="grid grid-cols-4 gap-4 relative">
            <div
              className="col-span-2 row-span-2 relative aspect-[4/3] cursor-pointer group"
              onClick={() => openDishGallery(currentImageIndex)}
            >
              <Image
                src={dish.photos[currentImageIndex] || "/placeholder.svg"}
                alt={`${dish.name} - Main Photo`}
                fill
                className="object-cover rounded-lg"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <Maximize2 className="w-8 h-8 text-white" />
              </div>
            </div>
            {dish.photos.slice(1, 5).map((photo: string, index: number) => (
              <div
                key={index}
                className="relative aspect-square cursor-pointer group"
                onClick={() => openDishGallery(index + 1)}
              >
                <Image
                  src={photo || "/placeholder.svg"}
                  alt={`${dish.name} - Photo ${index + 2}`}
                  fill
                  className="object-cover rounded-lg"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 16vw"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <Maximize2 className="w-6 h-6 text-white" />
                </div>
              </div>
            ))}
            <button
              className="absolute right-4 bottom-4 bg-white rounded-md px-4 py-2 shadow-md flex items-center gap-2 hover:bg-gray-100 transition-colors z-10"
              onClick={() => openDishGallery(0)}
            >
              <Maximize2 className="w-4 h-4" />
              <span>Show all photos</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div className="md:col-span-2 space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-semibold">Dish by {host.title}</h2>
              </div>
              <Button variant="outline" size="icon">
                <Heart className="h-4 w-4" />
              </Button>
            </div>

            <div className="border-t border-b py-6 flex justify-between">
              <div className="flex items-center">
                <Users className="w-6 h-6 mr-2" />
                <div>
                  <p className="font-semibold">Servings</p>
                  <p className="text-sm text-gray-500">
                    {dish.minServings}-{dish.maxServings} people
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <Clock className="w-6 h-6 mr-2" />
                <div>
                  <p className="font-semibold">Preparation</p>
                  <p className="text-sm text-gray-500">
                    {dish.premade ? "Pre-made" : "Made to order"}
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <DollarSign className="w-6 h-6 mr-2" />
                <div>
                  <p className="font-semibold">Price Range</p>
                  <p className="text-sm text-gray-500">
                    ${host.priceMin}-${host.priceMax}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">About this dish</h3>
              <p className="text-gray-600">{dish.description}</p>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Ingredients</h3>
              <ul className="list-disc list-inside text-gray-600">
                {dish.ingredients.map((ingredient: string, index: number) => (
                  <li key={index}>{ingredient}</li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Cuisine</h3>
              <p className="text-gray-600">
                {dish.cuisine.name} - {dish.subcuisine}
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Offering</h3>
              <div className="flex flex-wrap gap-2">
                {dish.offering.map((offer: string) => (
                  <span
                    key={offer}
                    className="bg-gray-100 px-3 py-1 rounded-full text-sm"
                  >
                    {offer}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div>
            <DishBookingCard
              dish={dish}
              onAddToCart={addToCart}
              dishInCart={dishInCart}
              isLoading={isLoading}
            />
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            About the dining location
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <p className="text-gray-600 mb-4">
                {host.title} offers a unique dining experience at{" "}
                {dish.diningLocation.locationName}.
              </p>
              <div className="flex items-center text-gray-600 mb-4">
                <MapPin className="w-5 h-5 mr-2" />
                <span>
                  {host.address.street}, {host.address.city},{" "}
                  {host.address.state} {host.address.postalCode}
                </span>
              </div>
              <p className="text-gray-600">
                Experience the ambiance of our{" "}
                {dish.diningLocation.locationName} while enjoying your meal. The
                location provides a perfect setting for a memorable dining
                experience.
              </p>
            </div>
            <div className="relative">
              <div
                className="aspect-[16/9] relative rounded-lg overflow-hidden cursor-pointer group"
                onClick={() => openLocationGallery(currentLocationImageIndex)}
              >
                <Image
                  src={
                    dish.diningLocation.images[currentLocationImageIndex] ||
                    "/placeholder.svg" ||
                    "/placeholder.svg"
                  }
                  alt={`${dish.diningLocation.locationName} - Image`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <Maximize2 className="w-8 h-8 text-white" />
                </div>
              </div>
              {dish.diningLocation.images.length > 1 && (
                <>
                  <button
                    onClick={prevLocationImage}
                    className="absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md"
                    aria-label="Previous location image"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  <button
                    onClick={nextLocationImage}
                    className="absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md"
                    aria-label="Next location image"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}
              {dish.diningLocation.images.length > 1 && (
                <button
                  className="absolute right-4 bottom-4 bg-white rounded-md px-4 py-2 shadow-md flex items-center gap-2 hover:bg-gray-100 transition-colors"
                  onClick={() => openLocationGallery(0)}
                >
                  <Maximize2 className="w-4 h-4" />
                  <span>View all</span>
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Reviews</h2>
          <div className="space-y-6">
            {dish.reviews ? (
              dish.reviews.map((review: any) => (
                <div key={review.id} className="border-b pb-6">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-200 rounded-full mr-3 flex items-center justify-center">
                        <span className="text-gray-600 font-semibold">
                          {review.author[0]}
                        </span>
                      </div>
                      <div>
                        <p className="font-semibold">{review.author}</p>
                        <p className="text-sm text-gray-500">{review.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Star className="w-5 h-5 text-yellow-400 mr-1" />
                      <span className="font-semibold">
                        {review.rating.toFixed(1)}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600">{review.comment}</p>
                </div>
              ))
            ) : (
              <p className="text-gray-600">No reviews yet</p>
            )}
          </div>
        </div>
      </div>

      {/* Image Gallery Drawer */}
      <ImageGalleryDrawer
        isOpen={galleryOpen}
        onClose={() => setGalleryOpen(false)}
        images={galleryImages}
        initialIndex={galleryInitialIndex}
        title={galleryTitle}
      />
    </React.Fragment>
  );
}
