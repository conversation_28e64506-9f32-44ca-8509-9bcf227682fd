import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Spinner } from "@/components/ui/spinner";
import { cn } from "@/lib/utils";

interface DishCardSkeletonProps {
  className?: string;
  showSpinner?: boolean;
  variant?: "default" | "compact" | "detailed";
}

export function DishCardSkeleton({ 
  className, 
  showSpinner = false, 
  variant = "default" 
}: DishCardSkeletonProps) {
  if (variant === "compact") {
    return (
      <div className={cn("w-full bg-white rounded-lg border p-3 space-y-2", className)}>
        <Skeleton className="w-full h-24 rounded-lg" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
        <div className="flex justify-between items-center">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-12" />
        </div>
        {showSpinner && (
          <div className="flex justify-center pt-2">
            <Spinner size="sm" className="text-primary" />
          </div>
        )}
      </div>
    );
  }

  if (variant === "detailed") {
    return (
      <div className={cn("w-80 border rounded-lg overflow-hidden bg-white", className)}>
        <Skeleton className="w-full h-48" />
        <div className="p-4 space-y-3">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <div className="flex justify-between items-center">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex flex-wrap gap-1">
            <Skeleton className="h-5 w-12 rounded-full" />
            <Skeleton className="h-5 w-16 rounded-full" />
            <Skeleton className="h-5 w-14 rounded-full" />
          </div>
          <Skeleton className="h-4 w-1/2" />
          {showSpinner && (
            <div className="flex justify-center pt-2">
              <Spinner size="sm" className="text-primary" />
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn("w-full h-full rounded-lg overflow-hidden bg-gray-100", className)}>
      <Skeleton className="aspect-square w-full" />
      <div className="p-4 space-y-2">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
        <Skeleton className="h-3 w-1/4" />
        {showSpinner && (
          <div className="flex justify-center pt-2">
            <Spinner size="sm" className="text-primary" />
          </div>
        )}
      </div>
    </div>
  );
}

interface DishCardLoadingOverlayProps {
  children: React.ReactNode;
  isLoading: boolean;
  className?: string;
}

export function DishCardLoadingOverlay({ 
  children, 
  isLoading, 
  className 
}: DishCardLoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
          <div className="flex flex-col items-center space-y-2">
            <Spinner size="md" className="text-primary" />
            <span className="text-sm text-gray-600">Loading...</span>
          </div>
        </div>
      )}
    </div>
  );
}

export default DishCardSkeleton;
