import React, { useMemo, useRef, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON>, Marker, InfoWindow } from "@react-google-maps/api";
import MapDishCard from "@/components/cards/MapDishCard";
import { Dish, Host } from "@/types/host";
import { useRouter } from "next/navigation";

interface GoogleMapComponentProps {
  isLoaded: boolean;
  loadError: Error | undefined;
  googleMapsApiKey: string;
  mapCenter: { lat: number; lng: number };
  mapZoom: number;
  mapHosts: Host[];
  selectedDish: Dish | null;
  handleDishMarkerClick: (dish: Dish, host: Host) => void;
  handleInfoWindowClose: () => void;
  hoveredDishId?: string | null;
  onBoundsChange?: (bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  }) => void;
  dishes?: Dish[];
  shouldFitBounds?: boolean;
  onBoundsFitted?: () => void;
}

// Helper function to calculate approximate pixel distance between two lat/lng positions
const calculatePixelDistance = (
  map: google.maps.Map,
  pos1: { lat: number; lng: number },
  pos2: { lat: number; lng: number }
): number => {
  const zoom = map.getZoom() || 10;
  const bounds = map.getBounds();

  if (!bounds) return 0;

  // Get map dimensions
  const mapDiv = map.getDiv();
  const mapWidth = mapDiv.offsetWidth;
  const mapHeight = mapDiv.offsetHeight;

  // Calculate the lat/lng span of the visible map
  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();
  const latSpan = ne.lat() - sw.lat();
  const lngSpan = ne.lng() - sw.lng();

  // Calculate pixels per degree
  const pixelsPerLat = mapHeight / latSpan;
  const pixelsPerLng = mapWidth / lngSpan;

  // Calculate pixel differences
  const latDiff = Math.abs(pos2.lat - pos1.lat) * pixelsPerLat;
  const lngDiff = Math.abs(pos2.lng - pos1.lng) * pixelsPerLng;

  // Return Euclidean distance in pixels
  return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
};

// Helper function to generate offset positions for dishes at the same location
const generateDishPositions = (
  dishes: Array<{ dish: Dish; host: Host }>,
  basePosition: { lat: number; lng: number }
) => {
  if (dishes.length === 1) {
    return [{ ...dishes[0], position: basePosition, priority: 0 }];
  }

  const positions: Array<{
    dish: Dish;
    host: Host;
    position: { lat: number; lng: number };
    priority: number;
  }> = [];
  const radius = 0.0002; // Small radius for positioning nearby markers

  dishes.forEach((item, index) => {
    if (index === 0) {
      // First dish stays at the original position with highest priority
      positions.push({ ...item, position: basePosition, priority: 0 });
    } else {
      // Calculate offset position in a circle around the base position
      const angle = (2 * Math.PI * index) / dishes.length;
      const offsetLat = basePosition.lat + radius * Math.cos(angle);
      const offsetLng = basePosition.lng + radius * Math.sin(angle);

      positions.push({
        ...item,
        position: { lat: offsetLat, lng: offsetLng },
        priority: index,
      });
    }
  });

  return positions;
};

// Helper function to group dishes by location
const groupDishesByLocation = (mapHosts: Host[]) => {
  const locationGroups = new Map<string, Array<{ dish: Dish; host: Host }>>();

  mapHosts.forEach((host) => {
    if (host.address?.latitude && host.address?.longitude && host.dishes) {
      const locationKey = `${host.address.latitude.toFixed(
        6
      )},${host.address.longitude.toFixed(6)}`;

      host.dishes.forEach((dish) => {
        if (!locationGroups.has(locationKey)) {
          locationGroups.set(locationKey, []);
        }
        locationGroups.get(locationKey)!.push({ dish, host });
      });
    }
  });

  const allDishMarkers: Array<{
    dish: Dish;
    host: Host;
    position: { lat: number; lng: number };
    priority: number;
  }> = [];

  locationGroups.forEach((dishesAtLocation, locationKey) => {
    const [lat, lng] = locationKey.split(",").map(Number);
    const basePosition = { lat, lng };

    const positionedDishes = generateDishPositions(
      dishesAtLocation,
      basePosition
    );
    allDishMarkers.push(...positionedDishes);
  });

  return allDishMarkers;
};

// Helper function to detect marker collisions and determine visibility
const calculateMarkerVisibility = (
  dishMarkers: Array<{
    dish: Dish;
    host: Host;
    position: { lat: number; lng: number };
    priority: number;
  }>,
  map: google.maps.Map | null,
  hoveredDishId: string | null,
  baseCollisionThreshold: number = 40
) => {
  if (!map) {
    return dishMarkers.map((marker) => ({
      ...marker,
      isVisible: true,
      clusterId: marker.dish._id,
    }));
  }

  const zoom = map.getZoom() || 10;

  // Adjust collision threshold based on zoom level
  // Higher zoom = smaller threshold (markers can be closer)
  // Lower zoom = larger threshold (markers need more space)
  const collisionThreshold = Math.max(
    20, // Minimum threshold
    baseCollisionThreshold * Math.pow(0.8, Math.max(0, zoom - 10))
  );

  // At very high zoom levels (16+), show all markers
  if (zoom >= 16) {
    return dishMarkers.map((marker) => ({
      ...marker,
      isVisible: true,
      clusterId: marker.dish._id,
    }));
  }

  const markersWithVisibility = dishMarkers.map((marker) => ({
    ...marker,
    isVisible: false,
    clusterId: "",
  }));

  // Group markers by collision clusters
  const clusters: Array<Array<number>> = [];
  const processed = new Set<number>();

  markersWithVisibility.forEach((marker, index) => {
    if (processed.has(index)) return;

    const cluster = [index];
    processed.add(index);

    // Find all markers that collide with this one
    markersWithVisibility.forEach((otherMarker, otherIndex) => {
      if (otherIndex === index || processed.has(otherIndex)) return;

      const distance = calculatePixelDistance(
        map,
        marker.position,
        otherMarker.position
      );
      if (distance < collisionThreshold) {
        cluster.push(otherIndex);
        processed.add(otherIndex);
      }
    });

    clusters.push(cluster);
  });

  // Determine visibility for each cluster
  clusters.forEach((cluster, clusterIndex) => {
    const clusterId = `cluster-${clusterIndex}`;

    // Check if any marker in this cluster is hovered
    const hoveredMarkerIndex = cluster.find(
      (index) => markersWithVisibility[index].dish._id === hoveredDishId
    );

    if (hoveredMarkerIndex !== undefined) {
      // Show only the hovered marker
      markersWithVisibility[hoveredMarkerIndex].isVisible = true;
      markersWithVisibility[hoveredMarkerIndex].clusterId = clusterId;
    } else {
      // Show only the highest priority marker (lowest priority number)
      const priorityMarkerIndex = cluster.reduce((bestIndex, currentIndex) => {
        return markersWithVisibility[currentIndex].priority <
          markersWithVisibility[bestIndex].priority
          ? currentIndex
          : bestIndex;
      });

      markersWithVisibility[priorityMarkerIndex].isVisible = true;
      markersWithVisibility[priorityMarkerIndex].clusterId = clusterId;
    }

    // Set cluster ID for all markers in the cluster
    cluster.forEach((index) => {
      markersWithVisibility[index].clusterId = clusterId;
    });
  });

  return markersWithVisibility;
};

const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({
  isLoaded,
  loadError,
  googleMapsApiKey,
  mapCenter,
  mapZoom,
  mapHosts,
  selectedDish,
  handleDishMarkerClick,
  handleInfoWindowClose,
  hoveredDishId,
  onBoundsChange,
  dishes = [],
  shouldFitBounds = false,
  onBoundsFitted,
}) => {
  const router = useRouter();
  const mapContainerStyle = {
    width: "100%",
    height: "100%",
    borderRadius: "0px",
  };

  const mapRef = useRef<google.maps.Map | null>(null);
  const [mapUpdateTrigger, setMapUpdateTrigger] = React.useState(0);
  const recalculationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced recalculation function
  const triggerRecalculation = useCallback(() => {
    if (recalculationTimeoutRef.current) {
      clearTimeout(recalculationTimeoutRef.current);
    }

    recalculationTimeoutRef.current = setTimeout(() => {
      setMapUpdateTrigger((prev) => prev + 1);
    }, 100); // 100ms debounce for smooth performance
  }, []);

  // Handle bounds change for dynamic loading and collision recalculation
  const handleBoundsChanged = useCallback(() => {
    if (!mapRef.current) return;

    // Trigger debounced marker visibility recalculation
    triggerRecalculation();

    if (onBoundsChange) {
      const bounds = mapRef.current.getBounds();
      if (bounds) {
        const ne = bounds.getNorthEast();
        const sw = bounds.getSouthWest();

        onBoundsChange({
          north: ne.lat(),
          south: sw.lat(),
          east: ne.lng(),
          west: sw.lng(),
        });
      }
    }
  }, [onBoundsChange, triggerRecalculation]);

  // Generate individual dish markers with proximity positioning
  const dishMarkers = useMemo(() => {
    return groupDishesByLocation(mapHosts);
  }, [mapHosts]);

  // Function to calculate bounds from all dish markers and fit the map
  const fitMapToBounds = useCallback(() => {
    if (!mapRef.current || dishMarkers.length === 0) return;

    const bounds = new window.google.maps.LatLngBounds();

    // Add all dish marker positions to bounds
    dishMarkers.forEach((marker) => {
      bounds.extend(
        new window.google.maps.LatLng(marker.position.lat, marker.position.lng)
      );
    });

    // Fit the map to show all markers with appropriate padding
    mapRef.current.fitBounds(bounds, {
      top: 50,
      right: 50,
      bottom: 50,
      left: 50,
    });

    console.log("Map bounds fitted to show all dish markers");
  }, [dishMarkers]);

  // Calculate marker visibility based on collision detection and zoom level
  const visibleMarkers = useMemo(() => {
    return calculateMarkerVisibility(
      dishMarkers,
      mapRef.current,
      hoveredDishId || null
    );
  }, [dishMarkers, hoveredDishId, mapUpdateTrigger]); // Include mapUpdateTrigger to recalculate on map changes

  // Find the host and position for the selected dish
  const selectedDishInfo = useMemo(() => {
    if (!selectedDish) return null;

    const dishMarker = visibleMarkers.find(
      (marker) => marker.dish._id === selectedDish._id
    );
    return dishMarker || null;
  }, [selectedDish, visibleMarkers]);

  // Create marker options with normal and hover states
  const getMarkerOptions = useCallback(
    (isHovered: boolean) => {
      if (!isLoaded) return null;

      const badgePath =
        "M 12,0 H 36 A 12,12 0 0 1 36,24 H 12 A 12,12 0 0 1 12,0 Z";
      return {
        path: badgePath,
        fillColor: isHovered ? "#ffffff" : "#FF5A5F",
        fillOpacity: 1,
        strokeColor: isHovered ? "#FF5A5F" : "#ffffff",
        strokeWeight: 1.5,
        rotation: 0,
        scale: isHovered ? 1.1 : 0.9,
        anchor: new window.google.maps.Point(24, 24),
        labelOrigin: new window.google.maps.Point(24, 12),
      };
    },
    [isLoaded]
  );

  // Default marker options
  const defaultMarkerOptions = useMemo(
    () => getMarkerOptions(false),
    [getMarkerOptions]
  );

  // Handle map click to close InfoWindow
  const handleMapClick = useCallback(() => {
    handleInfoWindowClose();
  }, [handleInfoWindowClose]);

  // Effect to trigger initial calculation when map is ready
  React.useEffect(() => {
    if (mapRef.current && dishMarkers.length > 0) {
      triggerRecalculation();
    }
  }, [dishMarkers, triggerRecalculation]);

  // Effect to fit bounds when triggered by parent component after search
  React.useEffect(() => {
    if (shouldFitBounds && mapRef.current && dishMarkers.length > 0) {
      // Add a small delay to ensure map is fully rendered and markers are positioned
      const timeoutId = setTimeout(() => {
        fitMapToBounds();
        // Notify parent that bounds have been fitted
        if (onBoundsFitted) {
          onBoundsFitted();
        }
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [shouldFitBounds, dishMarkers, fitMapToBounds, onBoundsFitted]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (recalculationTimeoutRef.current) {
        clearTimeout(recalculationTimeoutRef.current);
      }
    };
  }, []);

  if (!isLoaded && !loadError) {
    return (
      <div className="flex items-center justify-center h-full">
        Loading Maps...
      </div>
    );
  }

  if (loadError) {
    console.error("Google Maps Load Error:", loadError);
    return (
      <div className="flex items-center justify-center h-full text-red-500">
        Error loading maps. Please check the console or try again later.
      </div>
    );
  }

  if (!googleMapsApiKey) {
    return (
      <div className="flex items-center justify-center h-full text-red-500">
        Google Maps API key is missing.
      </div>
    );
  }

  return (
    <GoogleMap
      mapContainerStyle={mapContainerStyle}
      center={mapCenter}
      zoom={mapZoom}
      options={{
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: false,
        zoomControl: true,
        clickableIcons: false,
      }}
      onLoad={(map) => {
        console.log("Map instance loaded");
        mapRef.current = map;
        // Auto-zoom disabled: Only zoom when search has address and that address is geocoded
      }}
      onBoundsChanged={handleBoundsChanged}
      onClick={handleMapClick}
    >
      {/* Render Individual Dish Markers with Collision Detection */}
      {visibleMarkers
        .filter((marker) => marker.isVisible)
        .map((dishMarker, index) => {
          const { dish, host, position } = dishMarker;

          // Check if this dish is currently hovered
          const isDishHovered = dish._id === hoveredDishId;

          return (
            <Marker
              key={`${dish._id}-${index}`}
              position={position}
              onClick={() => handleDishMarkerClick(dish, host)}
              options={{
                icon: isDishHovered
                  ? getMarkerOptions(true)
                  : defaultMarkerOptions,
                zIndex: isDishHovered ? 1000 : 999,
              }}
              label={{
                text: `$${
                  dish.discount
                    ? dish.price * (1 - dish.discount / 100)
                    : dish.price
                }`,
                color: isDishHovered ? "#FF5A5F" : "white",
                fontWeight: "bold",
                fontSize: "12px",
              }}
            />
          );
        })}

      {/* InfoWindow */}
      {selectedDish && selectedDishInfo && (
        <InfoWindow
          position={selectedDishInfo.position}
          options={{
            pixelOffset: new window.google.maps.Size(0, -40),
            disableAutoPan: false,
            headerDisabled: true,
          }}
          onCloseClick={handleInfoWindowClose}
        >
          <div>
            <MapDishCard
              dish={selectedDish}
              hostTitle={selectedDishInfo.host.title}
              hostAddress={{
                street: selectedDishInfo.host.address.street,
                city: selectedDishInfo.host.address.city,
                state: selectedDishInfo.host.address.state,
              }}
              onClose={handleInfoWindowClose}
              onViewDetails={() => {
                router.push(`/dish/${selectedDish._id}`);
              }}
            />
          </div>
        </InfoWindow>
      )}
    </GoogleMap>
  );
};

export default GoogleMapComponent;
