import type React from "react";
import { useState, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  format,
  isSameDay,
  isAfter,
  startOfToday,
  parse,
  addMinutes,
} from "date-fns";
import { Plus, Minus, CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { DishCardLoadingOverlay } from "@/components/ui/dish-card-skeleton";

interface DishBookingCardProps {
  dish: {
    currency: string;
    price: number;
    discount?: number;
    availability: {
      date: Date;
      startTime: string;
      endTime: string;
    }[];
    minServings: number;
    maxServings: number;
  };
  dishInCart: boolean;
  isLoading?: boolean;
  onAddToCart: (
    selectedDate: Date | null,
    quantity: number,
    orderType: "Delivery" | "Take Away" | "Dine In"
  ) => void;
}

export function DishBookingCard({
  dish,
  dishInCart,
  isLoading = false,
  onAddToCart,
}: DishBookingCardProps) {
  console.log("DISH IN CART", dishInCart);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string | undefined>(
    undefined
  );
  const [quantity, setQuantity] = useState(1);
  const [servings, setServings] = useState(dish.minServings);
  const [orderType, setOrderType] = useState<
    "Delivery" | "Take Away" | "Dine In" | null
  >("Dine In");

  const today = startOfToday();

  const availableDates = useMemo(
    () =>
      dish.availability
        .map((slot) => new Date(slot.date))
        .filter((date) => isAfter(date, today) || isSameDay(date, today)),
    [dish.availability, today]
  );

  const generateTimeSlots = (
    start: string,
    end: string,
    date: Date
  ): string[] => {
    const slots: string[] = [];
    const [startHour, startMinute] = start.split(":").map(Number);
    const [endHour, endMinute] = end.split(":").map(Number);

    let currentTime = new Date(date);
    currentTime.setHours(startHour, startMinute, 0);
    const endTime = new Date(date);
    endTime.setHours(endHour, endMinute, 0);

    const now = new Date();

    while (currentTime <= endTime) {
      if (currentTime > now) {
        slots.push(
          currentTime.toLocaleTimeString(undefined, { timeStyle: "short" })
        );
      }
      currentTime = addMinutes(currentTime, 30);
    }

    return slots;
  };

  const availableTimesForSelectedDate = useMemo(() => {
    if (!selectedDate) return [];
    return dish.availability
      .filter((slot) => isSameDay(new Date(slot.date), selectedDate))
      .flatMap((slot) =>
        generateTimeSlots(slot.startTime, slot.endTime, selectedDate)
      );
  }, [selectedDate, dish.availability, generateTimeSlots]); // Added generateTimeSlots to dependencies

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setSelectedTime(undefined);
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  const handleQuantityChange = (increment: number) => {
    setQuantity((prev) => Math.max(1, prev + increment));
  };

  const handleServingsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number.parseInt(event.target.value, 10);
    if (
      !isNaN(value) &&
      value >= dish.minServings &&
      value <= dish.maxServings
    ) {
      setServings(value);
    }
  };

  const handleAddToCart = () => {
    if (selectedDate && selectedTime && orderType) {
      const [hours, minutes] = selectedTime.split(":").map(Number);
      const orderDateTime = new Date(selectedDate);
      orderDateTime.setHours(hours, minutes, 0);
      onAddToCart(orderDateTime, quantity, orderType);
    } else if (orderType === "Delivery") {
      onAddToCart(null, quantity, orderType);
    }
  };

  const totalPrice = dish.price * quantity;

  if (dishInCart) {
    return (
      <DishCardLoadingOverlay isLoading={isLoading}>
        <div className="flex flex-col">
          <Card className="sticky top-28 rounded-none border-b-0">
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <p className="text-2xl font-bold">
                    {dish.currency} {totalPrice.toFixed(2)}
                  </p>
                  {dish.discount && (
                    <p className="text-sm line-through text-gray-500">
                      {dish.currency}{" "}
                      {(totalPrice / (1 - dish.discount / 100)).toFixed(2)}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal mb-4"
                  disabled
                >
                  Already in cart
                </Button>
              </div>
              <Button>
                <a href="/cart">Go to cart</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DishCardLoadingOverlay>
    );
  }

  return (
    <DishCardLoadingOverlay isLoading={isLoading}>
      <div className="flex flex-col">
        <Card className="sticky top-28 rounded-none border-b-0">
          <div className="my-4">
            <div className="flex items-center justify-evenly space-x-2">
              {availableDates.length > 0 && (
                <Button
                  variant={orderType === "Dine In" ? "default" : "outline"}
                  onClick={() => setOrderType("Dine In")}
                  disabled={isLoading}
                >
                  Dine In
                </Button>
              )}
              <Button
                variant={orderType === "Delivery" ? "default" : "outline"}
                onClick={() => setOrderType("Delivery")}
                disabled={isLoading}
              >
                Delivery
              </Button>
              {availableDates.length > 0 && (
                <Button
                  variant={orderType === "Take Away" ? "default" : "outline"}
                  onClick={() => setOrderType("Take Away")}
                  disabled={isLoading}
                >
                  Take Away
                </Button>
              )}
            </div>
          </div>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-2xl font-bold">
                  {dish.currency} {totalPrice.toFixed(2)}
                </p>
                {dish.discount && (
                  <p className="text-sm line-through text-gray-500">
                    {dish.currency}{" "}
                    {(totalPrice / (1 - dish.discount / 100)).toFixed(2)}
                  </p>
                )}
              </div>
            </div>

            {(orderType === "Dine In" || orderType === "Take Away") && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal mb-4",
                      !selectedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? (
                      format(selectedDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    initialFocus
                    disabled={(date) =>
                      !availableDates.some((availableDate) =>
                        isSameDay(date, availableDate)
                      ) || date < today
                    }
                  />
                </PopoverContent>
              </Popover>
            )}
            {selectedDate && orderType !== "Delivery" && (
              <div className="mb-4">
                <Select onValueChange={handleTimeSelect} value={selectedTime}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a time" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTimesForSelectedDate.map((time, index) => (
                      <SelectItem key={index} value={time}>
                        {time}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-gray-700">
                Quantity:
              </span>
              <div className="flex items-center">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleQuantityChange(-1)}
                  disabled={quantity === 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="mx-2 text-lg font-semibold">{quantity}</span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleQuantityChange(1)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Button
              className="w-full mb-4"
              onClick={handleAddToCart}
              disabled={
                isLoading ||
                ((!selectedDate || !selectedTime) && orderType !== "Delivery")
              }
            >
              {isLoading
                ? "Loading..."
                : selectedDate && selectedTime && orderType !== "Delivery"
                ? orderType === "Dine In"
                  ? `Add to cart to Dine in at ${format(
                      selectedDate,
                      "MMM d"
                    )} ${selectedTime}`
                  : `Add to cart to Take Away at ${format(
                      selectedDate,
                      "MMM d"
                    )} ${selectedTime}`
                : orderType !== "Delivery"
                ? "Select order type, date, and time"
                : "Add to cart for Delivery"}
            </Button>
          </CardContent>
        </Card>
      </div>
    </DishCardLoadingOverlay>
  );
}
