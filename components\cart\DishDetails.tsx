import React, { useEffect, useState } from "react";
import { useCartContext } from "@/context/hooks/use-cart-hook";
import { getDishDetails } from "@/api/host";
import HostCard from "@/components/cards/CustomCard";
import { QuantitySelector } from "./EditableQuantity";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Spinner } from "@/components/ui/spinner";
import { Skeleton } from "@/components/ui/skeleton";

const CartRender = () => {
  const { cartItems, updateCartItem } = useCartContext();
  const [detailedItems, setDetailedItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingItems, setLoadingItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchDishDetails = async () => {
      if (cartItems.length === 0) {
        setDetailedItems([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setLoadingItems(new Set(cartItems.map((item: any) => item.dishId)));

      try {
        const details = await Promise.all(
          cartItems.map(async (item: any) => {
            try {
              const dishDetail = await getDishDetails(item.dishId);
              // Remove this item from loading set
              setLoadingItems((prev) => {
                const newSet = new Set(prev);
                newSet.delete(item.dishId);
                return newSet;
              });
              return { ...dishDetail.data, quantity: item.quantity };
            } catch (error) {
              console.error(
                `Failed to fetch dish details for ${item.dishId}:`,
                error
              );
              // Remove this item from loading set even on error
              setLoadingItems((prev) => {
                const newSet = new Set(prev);
                newSet.delete(item.dishId);
                return newSet;
              });
              // Return a placeholder object for failed items
              return {
                _id: item.dishId,
                name: "Failed to load dish",
                price: 0,
                quantity: item.quantity,
                error: true,
              };
            }
          })
        );
        setDetailedItems(details);
      } catch (error) {
        console.error("Failed to fetch dish details:", error);
        setDetailedItems([]);
      } finally {
        setIsLoading(false);
        setLoadingItems(new Set());
      }
    };

    fetchDishDetails();
  }, [cartItems]);

  const renderCartButton = (item: any) => {
    return (
      <div className="w-full flex justify-center mt-2">
        <QuantitySelector
          quantity={item?.quantity}
          onQuantityChange={(newQuantity) =>
            updateCartItem(
              item?._id,
              newQuantity,
              item?.hostId || "",
              item?.price || 0
            )
          }
          min={0}
          max={99}
        />
      </div>
    );
  };

  const subtotal = detailedItems.reduce(
    (acc, item) => acc + item?.price * item.quantity,
    0
  );
  const total = subtotal; // Add tax or other charges here if needed

  return (
    <div className="w-full max-w-xl mx-auto bg-white rounded-xl border py-6 px-16 space-y-6">
      <div className="flex justify-between items-center">
        <span className="text-lg font-medium">Selected Dishes</span>
        <span className="text-lg font-medium">
          {cartItems?.length ?? 0} Dishes
        </span>
      </div>

      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {/* Show loading skeletons while fetching */}
          {isLoading && cartItems.length > 0 && (
            <>
              {cartItems.map((_, index) => (
                <CarouselItem
                  key={`loading-${index}`}
                  className="pl-2 md:pl-4 basis-auto"
                >
                  <div className="w-60 h-54 bg-white rounded-lg border p-4 space-y-3">
                    <Skeleton className="w-full h-32 rounded-lg" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                    <div className="flex justify-center">
                      <Spinner size="sm" className="text-primary" />
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </>
          )}

          {/* Show actual dish cards when loaded */}
          {!isLoading &&
            detailedItems?.length > 0 &&
            detailedItems.map((item, index) => (
              <CarouselItem
                key={item._id || index}
                className="pl-2 md:pl-4 basis-auto"
              >
                {item.error ? (
                  // Error state for individual dish
                  <div className="w-60 h-54 bg-white rounded-lg border p-4 flex flex-col items-center justify-center space-y-2">
                    <div className="text-red-500">
                      <svg
                        className="w-8 h-8"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-600 text-center">
                      Failed to load dish
                    </p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.location.reload()}
                    >
                      Retry
                    </Button>
                  </div>
                ) : (
                  <HostCard
                    width="60"
                    height="54"
                    onClick={() => {}}
                    images={item?.photos ?? []}
                    title={item?.name ?? "N/A"}
                    locationName={item?.description ?? "N/A"}
                    rating={4.8}
                    priceRange={`$${item?.price ?? 0}`}
                    renderExtra={() => renderCartButton(item)}
                  />
                )}
              </CarouselItem>
            ))}

          {/* Show message when no items */}
          {!isLoading &&
            detailedItems?.length === 0 &&
            cartItems.length === 0 && (
              <CarouselItem className="pl-2 md:pl-4 basis-full">
                <div className="w-full h-32 flex items-center justify-center text-gray-500">
                  <p>No dishes in cart</p>
                </div>
              </CarouselItem>
            )}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>

      <div className="space-y-4 pt-4 border-t">
        <div className="flex justify-between items-center text-base">
          <span className="text-gray-600">Subtotal</span>
          <span className="font-medium">${subtotal.toFixed(2)}</span>
        </div>

        <div className="flex justify-between items-center text-lg font-semibold pt-4 border-t">
          <span>Total</span>
          <span>${total.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
};

export default CartRender;
